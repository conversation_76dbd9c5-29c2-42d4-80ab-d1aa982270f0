{"name": "pasteflow", "version": "1.0.0", "main": "build/main/main.mjs", "bin": {"pasteflow": "cli/dist/index.mjs", "pf": "cli/dist/index.mjs"}, "scripts": {"start": "electron .", "build:scripts": "tsc -p tsconfig.scripts.json", "dev": "vite", "dev:electron": "tsx dev.ts", "build": "vite build", "build-electron": "tsx build.ts", "postinstall": "electron-builder install-app-deps", "rebuild:electron": "npx electron-rebuild -f -w better-sqlite3 -v 34.3.0", "build-native": "npm rebuild better-sqlite3 --build-from-source", "verify-build": "tsx scripts/verify-build.ts", "test-build": "tsx scripts/test-local-build.ts", "test-build:mac": "tsx scripts/test-local-build.ts mac", "test-build:win": "tsx scripts/test-local-build.ts win", "test-build:linux": "tsx scripts/test-local-build.ts linux", "debug-gh-release": "git tag debug-v$(date +'%Y%m%d%H%M%S') && git push origin --tags", "package": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder --publish=never", "build:main:esm": "tsup --config tsup.config.ts", "build:main:esm:watch": "tsup --config tsup.config.ts --watch", "start:esm": "npm run build:main:esm && electron build/main/main.mjs", "package:esm": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder --publish=never", "package:mac": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder --mac --publish=never", "package:win": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder --win --publish=never", "package:linux": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder --linux --publish=never", "package:all": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder -mwl --publish=never", "release": "vite build && npm run build:main:esm && npm run build:scripts && electron-builder --publish=onTagOrDraft", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "lint:strict": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:filenames": "eslint . --ext ts,tsx --rule \"filenames/match-regex: error\" --rule \"filenames/match-exported: error\" --rule \"filenames/no-index: off\"", "preview": "vite preview", "test": "jest --maxWorkers=2", "test:unit": "jest --testPathPattern='(^((?!integration|e2e).)*test\\.(ts|tsx)$)' --maxWorkers=2", "test:integration": "jest --testPathPattern='integration' --maxWorkers=1", "test:e2e": "jest --testPathPattern='e2e' --maxWorkers=1", "test:ci": "jest --ci --coverage --maxWorkers=2", "test:debug": "node --inspect-brk ./node_modules/.bin/jest --runInBand", "test:failed": "jest --onlyFailures", "test:watch": "jest --watch --maxWorkers=2", "test:mock-check": "npx tsx scripts/test-quality/mock-count-checker.ts", "test:assertion-check": "npx tsx scripts/test-quality/assertion-density-checker.ts", "test:quality-full": "npm run test:mock-check && npm run test:assertion-check && npm test", "build:cli": "tsup --config tsup.config.ts", "build:cli:watch": "tsup --config tsup.config.ts --watch", "cli:dev": "tsx cli/src/index.ts", "link:cli": "npm run build:cli && npm link"}, "keywords": ["clipboard", "code", "developer-tools", "electron", "file-viewer"], "author": {"name": "nico.bailon", "email": "<EMAIL>"}, "license": "MIT", "description": "A modern file viewer application for developers to easily navigate, search, and copy code from repositories.", "build": {"appId": "com.nbailon.pasteflow", "productName": "PasteFlow", "directories": {"output": "release-builds"}, "files": ["dist/**/*", "build/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "icon": "public/favicon.icns", "target": ["dmg", "zip"], "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": ["nsis", "portable"], "icon": "public/favicon.ico"}, "linux": {"target": ["AppImage", "deb", "rpm"], "category": "Development", "icon": "public/favicon.png"}, "asarUnpack": ["node_modules/ignore/**", "node_modules/tiktoken/**", "node_modules/better-sqlite3/**", "node_modules/node-pty/**"], "npmRebuild": true, "buildDependenciesFromSource": false, "asar": true, "afterSign": "build/scripts/notarize.js", "publish": ["github"]}, "devDependencies": {"@electron/notarize": "^2.5.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/better-sqlite3": "^7.6.13", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.10.5", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "electron": "^34.3.0", "electron-builder": "^24.13.3", "eslint": "^8.55.0", "eslint-import-resolver-typescript": "^4.2.7", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-json": "^4.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-sonarjs": "^0.23.0", "eslint-plugin-unicorn": "^46.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsonc-eslint-parser": "^2.4.0", "prettier": "^2.8.8", "ts-jest": "^29.1.2", "tsc-alias": "^1.8.16", "tsup": "^8.5.0", "tsx": "^4.20.4", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-top-level-await": "^1.6.0", "vite-plugin-wasm": "^3.5.0", "vite-tsconfig-paths": "^4.3.2"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.12", "@ai-sdk/groq": "^2.0.19", "@ai-sdk/openai": "^2.0.25", "@ai-sdk/react": "^2.0.35", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/themes": "^3.2.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@xmldom/xmldom": "^0.9.6", "ai": "^5.0.35", "axios": "^1.7.2", "better-sqlite3": "^11.7.0", "commander": "^11.1.0", "diff": "^5.2.0", "express": "^4.19.2", "ignore": "^7.0.3", "lucide-react": "^0.477.0", "node-pty": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-syntax-highlighter": "^15.6.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "tiktoken": "^1.0.20", "unified-diff": "^4.0.0", "uuid": "^11.1.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "zod": "^3.23.8"}}